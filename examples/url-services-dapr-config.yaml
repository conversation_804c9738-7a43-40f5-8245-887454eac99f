# Dapr Multi-App Run Configuration for URL Services
# 使用命令: dapr run -f examples/url-services-dapr-config.yaml

version: 1
common:
  resourcesPath: ./dapr/components
  configFilePath: ./dapr/config.yaml
  
apps:
  # 爬虫任务管理服务
  - appID: crawler-task-manager
    appDirPath: ./
    appPort: 8080
    command: ["go", "run", "cmd/task_mgr/main.go"]
    env:
      # 服务器配置
      SERVER_PORT: "8080"
      SERVER_HOST: "0.0.0.0"
      
      # Dapr配置
      DAPR_APP_ID: "crawler-task-manager"
      DAPR_APP_PORT: "8080"
      DAPR_STATE_STORE: "statestore"
      DAPR_PUBSUB_NAME: "pubsub"
      DAPR_SECRET_STORE: "secretstore"
      
      # 开发环境配置
      GIN_MODE: "debug"
      LOG_LEVEL: "debug"
      
    # Dapr sidecar配置
    daprHTTPPort: 3500
    daprGRPCPort: 50001
    daprHTTPMaxRequestSize: 4
    daprHTTPReadBufferSize: 4
    enableProfiling: true
    logLevel: info
    appLogDestination: console
    daprdLogDestination: console
    
    # 健康检查配置
    appHealthCheckPath: "/health"
    appHealthProbeInterval: 5
    appHealthProbeTimeout: 3
    appHealthThreshold: 3

  # URL调度服务
  - appID: crawler-url-scheduler
    appDirPath: ./examples
    command: ["go", "run", "url_scheduler_service.go"]
    env:
      # Dapr配置
      DAPR_APP_ID: "crawler-url-scheduler"
      
      # 开发环境配置
      LOG_LEVEL: "debug"
      
    # Dapr sidecar配置
    daprHTTPPort: 3501
    daprGRPCPort: 50002
    daprHTTPMaxRequestSize: 4
    daprHTTPReadBufferSize: 4
    enableProfiling: true
    logLevel: info
    appLogDestination: console
    daprdLogDestination: console

  # URL下载服务
  - appID: crawler-url-downloader
    appDirPath: ./examples
    appPort: 8081
    command: ["go", "run", "url_downloader_service.go"]
    env:
      # Dapr配置
      DAPR_APP_ID: "crawler-url-downloader"
      DAPR_APP_PORT: "8081"
      
      # 开发环境配置
      LOG_LEVEL: "debug"
      
    # Dapr sidecar配置
    daprHTTPPort: 3502
    daprGRPCPort: 50003
    daprHTTPMaxRequestSize: 4
    daprHTTPReadBufferSize: 4
    enableProfiling: true
    logLevel: info
    appLogDestination: console
    daprdLogDestination: console
