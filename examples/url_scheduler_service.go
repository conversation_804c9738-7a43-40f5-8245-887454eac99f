package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"time"

	"github.com/dapr/go-sdk/client"
)

// URLSchedulerService URL调度服务
type URLSchedulerService struct {
	daprClient       client.Client
	taskManagerAppID string
	pubsubName       string
	topicName        string
}

// NewURLSchedulerService 创建URL调度服务
func NewURLSchedulerService(taskManagerAppID, pubsubName, topicName string) (*URLSchedulerService, error) {
	daprClient, err := client.NewClient()
	if err != nil {
		return nil, fmt.Errorf("创建Dapr客户端失败: %w", err)
	}

	return &URLSchedulerService{
		daprClient:       daprClient,
		taskManagerAppID: taskManagerAppID,
		pubsubName:       pubsubName,
		topicName:        topicName,
	}, nil
}

// Start 启动URL调度服务
func (s *URLSchedulerService) Start(ctx context.Context) error {
	log.Println("启动URL调度服务...")

	// 启动定期获取URL并发布到Pubsub的goroutine
	go s.scheduleURLsLoop(ctx)

	log.Println("URL调度服务已启动")
	return nil
}

// TODO: 实现事件处理功能
// handleTaskCreated 处理任务创建事件（暂未实现）
// func (s *URLSchedulerService) handleTaskCreated(ctx context.Context, event TaskCreatedEvent) error {
//     log.Printf("任务创建: ID=%s, Name=%s, URLCount=%d", event.TaskID, event.TaskName, event.URLCount)
//     // 可以在这里添加特定的处理逻辑，比如根据任务优先级调整处理策略
//     return nil
// }

// scheduleURLsLoop 定期获取URL并发布到Pubsub
func (s *URLSchedulerService) scheduleURLsLoop(ctx context.Context) {
	ticker := time.NewTicker(5 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			s.scheduleURLs(ctx)
		}
	}
}

// scheduleURLs 获取待处理的URL并发布到Pubsub
func (s *URLSchedulerService) scheduleURLs(ctx context.Context) {
	// 获取待处理的URL
	urls, err := s.getPendingURLs(ctx, 5)
	if err != nil {
		log.Printf("获取待处理URL失败: %v", err)
		return
	}

	if len(urls) == 0 {
		return
	}

	log.Printf("获取到 %d 个待处理URL，准备发布到Pubsub", len(urls))

	// 发布URL到Pubsub
	for _, url := range urls {
		go s.publishURL(ctx, url)
	}
}

// getPendingURLs 获取待处理的URL
func (s *URLSchedulerService) getPendingURLs(ctx context.Context, limit int) ([]URLQueueItem, error) {
	// 构建请求路径，包含查询参数
	methodPath := fmt.Sprintf("api/v1/urls/pending?limit=%d", limit)

	// 使用 Dapr 服务调用
	resp, err := s.daprClient.InvokeMethod(ctx, s.taskManagerAppID, methodPath, "GET")
	if err != nil {
		return nil, fmt.Errorf("调用taskmanager服务失败: %w", err)
	}

	var response struct {
		Success bool `json:"success"`
		Data    struct {
			URLs []URLQueueItem `json:"urls"`
		} `json:"data"`
	}

	if err := json.Unmarshal(resp, &response); err != nil {
		return nil, fmt.Errorf("解析响应数据失败: %w", err)
	}

	if !response.Success {
		return nil, fmt.Errorf("获取待处理URL失败")
	}

	log.Printf("成功获取到 %d 个待处理URL", len(response.Data.URLs))
	return response.Data.URLs, nil
}

// publishURL 将URL发布到Pubsub
func (s *URLSchedulerService) publishURL(ctx context.Context, urlItem URLQueueItem) {
	log.Printf("发布URL到Pubsub: ID=%d, URL=%s", urlItem.ID, urlItem.URL)

	// 更新状态为scheduled
	if err := s.updateURLStatus(ctx, urlItem.ID, "scheduled", ""); err != nil {
		log.Printf("更新URL状态为scheduled失败: %v", err)
		return
	}

	// 发布到Pubsub
	data, err := json.Marshal(urlItem)
	if err != nil {
		log.Printf("序列化URL数据失败: %v", err)
		s.updateURLStatus(ctx, urlItem.ID, "failed", fmt.Sprintf("序列化失败: %v", err))
		return
	}

	if err := s.daprClient.PublishEvent(ctx, s.pubsubName, s.topicName, data); err != nil {
		log.Printf("发布URL到Pubsub失败: %v", err)
		s.updateURLStatus(ctx, urlItem.ID, "failed", fmt.Sprintf("发布失败: %v", err))
		return
	}

	log.Printf("成功发布URL到Pubsub: ID=%d", urlItem.ID)
}

// updateURLStatus 更新URL状态
func (s *URLSchedulerService) updateURLStatus(ctx context.Context, urlID int64, status, errorMessage string) error {
	// 构建请求路径
	methodPath := fmt.Sprintf("api/v1/urls/%d/status", urlID)

	// 构建请求体
	payload := map[string]string{
		"status": status,
	}
	if errorMessage != "" {
		payload["error_message"] = errorMessage
	}

	data, err := json.Marshal(payload)
	if err != nil {
		return fmt.Errorf("序列化请求数据失败: %w", err)
	}

	// 使用 Dapr 服务调用
	content := &client.DataContent{
		ContentType: "application/json",
		Data:        data,
	}

	_, err = s.daprClient.InvokeMethodWithContent(ctx, s.taskManagerAppID, methodPath, "PUT", content)
	if err != nil {
		return fmt.Errorf("调用taskmanager服务更新URL状态失败: %w", err)
	}

	log.Printf("通过Dapr成功更新URL状态: ID=%d, Status=%s", urlID, status)
	return nil
}

// Close 关闭服务
func (s *URLSchedulerService) Close() error {
	if s.daprClient != nil {
		s.daprClient.Close()
	}
	return nil
}

func main() {
	// 创建URL调度服务，使用 Dapr 应用ID
	scheduler, err := NewURLSchedulerService("crawler-task-manager", "pubsub", "url-download")
	if err != nil {
		log.Fatalf("创建URL调度服务失败: %v", err)
	}
	defer scheduler.Close()

	// 启动服务
	ctx := context.Background()
	if err := scheduler.Start(ctx); err != nil {
		log.Fatalf("启动URL调度服务失败: %v", err)
	}

	// 等待中断信号
	select {}
}
