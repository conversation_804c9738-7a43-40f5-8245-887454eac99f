# URL 服务架构说明

## 概述

本项目将 URL 处理功能重构为两个独立的微服务：

1. **URL 调度服务** (`url_scheduler_service.go`) - 负责从数据库获取待处理的 URL 并发布到 Pubsub
2. **URL 下载服务** (`url_downloader_service.go`) - 订阅 Pubsub 消息并执行实际的 URL 下载

## 架构图

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Task Manager  │    │ URL Scheduler   │    │ URL Downloader  │
│     Service     │    │    Service      │    │    Service      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │ 1. Get pending URLs   │                       │
         │◄──────────────────────┤                       │
         │                       │                       │
         │ 2. Return URLs        │                       │
         ├──────────────────────►│                       │
         │                       │                       │
         │                       │ 3. Publish URL        │
         │                       ├──────────────────────►│
         │                       │    (via Pubsub)       │
         │                       │                       │
         │ 4. Update URL status  │                       │
         │◄──────────────────────┼───────────────────────┤
         │                       │                       │
```

## 服务详情

### URL 调度服务 (URL Scheduler Service)

**职责：**
- 定期从任务管理服务获取待处理的 URL
- 将 URL 发布到 Pubsub 主题
- 更新 URL 状态为 "scheduled"

**配置：**
- App ID: `crawler-url-scheduler`
- Dapr HTTP Port: 3501
- Dapr GRPC Port: 50002

### URL 下载服务 (URL Downloader Service)

**职责：**
- 订阅 Pubsub 主题接收 URL 下载任务
- 执行实际的 HTTP 请求下载
- 更新 URL 状态（processing → completed/failed）

**配置：**
- App ID: `crawler-url-downloader`
- App Port: 8081
- Dapr HTTP Port: 3502
- Dapr GRPC Port: 50003

## Pubsub 配置

**主题名称：** `url-download`
**Pubsub 组件：** `pubsub` (Redis)

**消息格式：**
```json
{
  "id": 123,
  "task_id": "task-uuid",
  "url": "https://example.com",
  "method": "GET",
  "body": "",
  "priority": 1
}
```

## 启动服务

### 方式一：使用 Dapr Multi-App Run（推荐）

```bash
# 启动所有服务（任务管理 + URL调度 + URL下载）
dapr run -f examples/url-services-dapr-config.yaml
```

### 方式二：单独启动服务

```bash
# 启动任务管理服务
dapr run --app-id crawler-task-manager --app-port 8080 --dapr-http-port 3500 --dapr-grpc-port 50001 --components-path ./dapr/components --config ./dapr/config.yaml -- go run cmd/task_mgr/main.go

# 启动URL调度服务
dapr run --app-id crawler-url-scheduler --dapr-http-port 3501 --dapr-grpc-port 50002 --components-path ./dapr/components --config ./dapr/config.yaml -- go run examples/url_scheduler_service.go

# 启动URL下载服务
dapr run --app-id crawler-url-downloader --app-port 8081 --dapr-http-port 3502 --dapr-grpc-port 50003 --components-path ./dapr/components --config ./dapr/config.yaml -- go run examples/url_downloader_service.go
```

## 工作流程

1. **任务创建：** 用户通过任务管理服务创建爬虫任务，初始 URL 被保存到数据库
2. **URL 调度：** URL 调度服务定期查询待处理的 URL（状态为 "pending"）
3. **状态更新：** 调度服务将 URL 状态更新为 "scheduled"
4. **消息发布：** 调度服务将 URL 信息发布到 Pubsub 主题
5. **消息接收：** 下载服务订阅 Pubsub 主题，接收 URL 下载任务
6. **状态更新：** 下载服务将 URL 状态更新为 "processing"
7. **执行下载：** 下载服务执行实际的 HTTP 请求
8. **完成处理：** 下载服务将 URL 状态更新为 "completed" 或 "failed"

## URL 状态流转

```
pending → scheduled → processing → completed/failed
```

## 优势

1. **解耦：** URL 调度和下载逻辑完全分离
2. **可扩展：** 可以独立扩展调度服务和下载服务
3. **容错：** Pubsub 提供消息持久化和重试机制
4. **监控：** 每个服务可以独立监控和调试
5. **灵活：** 可以根据需要调整调度频率和下载并发数

## 注意事项

1. 确保 Redis 服务正在运行（Pubsub 依赖）
2. 确保 MySQL 服务正在运行（状态存储依赖）
3. 服务启动顺序：任务管理服务 → URL调度服务 → URL下载服务
4. 检查端口冲突：8080, 8081, 3500-3502, 50001-50003
