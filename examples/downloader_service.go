package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"time"

	"github.com/dapr/go-sdk/client"
)

// URLQueueItem URL队列项结构（简化版）
type URLQueueItem struct {
	ID       int64  `json:"id"`
	TaskID   string `json:"task_id"`
	URL      string `json:"url"`
	Method   string `json:"method"`
	Body     string `json:"body,omitempty"`
	Priority int    `json:"priority"`
}

// TaskCreatedEvent 任务创建事件结构
type TaskCreatedEvent struct {
	TaskID    string    `json:"task_id"`
	TaskName  string    `json:"task_name"`
	Priority  int       `json:"priority"`
	URLCount  int       `json:"url_count"`
	CreatedAt time.Time `json:"created_at"`
}

// DownloaderService 下载器服务
type DownloaderService struct {
	daprClient       client.Client
	taskManagerAppID string
	httpClient       *http.Client
}

// NewDownloaderService 创建下载器服务
func NewDownloaderService(taskManagerAppID string) (*DownloaderService, error) {
	daprClient, err := client.NewClient()
	if err != nil {
		return nil, fmt.Errorf("创建Dapr客户端失败: %w", err)
	}

	return &DownloaderService{
		daprClient:       daprClient,
		taskManagerAppID: taskManagerAppID,
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
	}, nil
}

// Start 启动下载器服务
func (d *DownloaderService) Start(ctx context.Context) error {
	log.Println("启动下载器服务...")

	// TODO: 实现事件订阅功能
	// 目前先使用定期轮询的方式获取待处理URL
	log.Println("注意：事件订阅功能暂未实现，使用定期轮询模式")

	// 启动定期处理待处理URL的goroutine
	go d.processURLsLoop(ctx)

	log.Println("下载器服务已启动")
	return nil
}

// TODO: 实现事件处理功能
// handleTaskCreated 处理任务创建事件（暂未实现）
// func (d *DownloaderService) handleTaskCreated(ctx context.Context, event TaskCreatedEvent) error {
//     log.Printf("任务创建: ID=%s, Name=%s, URLCount=%d", event.TaskID, event.TaskName, event.URLCount)
//     // 可以在这里添加特定的处理逻辑，比如根据任务优先级调整处理策略
//     return nil
// }

// processURLsLoop 定期处理待处理的URL
func (d *DownloaderService) processURLsLoop(ctx context.Context) {
	ticker := time.NewTicker(5 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			d.processURLs(ctx)
		}
	}
}

// processURLs 处理待处理的URL
func (d *DownloaderService) processURLs(ctx context.Context) {
	// 获取待处理的URL
	urls, err := d.getPendingURLs(ctx, 5)
	if err != nil {
		log.Printf("获取待处理URL失败: %v", err)
		return
	}

	if len(urls) == 0 {
		return
	}

	log.Printf("获取到 %d 个待处理URL", len(urls))

	// 并发处理URL
	for _, url := range urls {
		go d.processURL(ctx, url)
	}
}

// getPendingURLs 获取待处理的URL
func (d *DownloaderService) getPendingURLs(ctx context.Context, limit int) ([]URLQueueItem, error) {
	// 构建请求路径，包含查询参数
	methodPath := fmt.Sprintf("api/v1/urls/pending?limit=%d", limit)

	// 使用 Dapr 服务调用
	resp, err := d.daprClient.InvokeMethod(ctx, d.taskManagerAppID, methodPath, "GET")
	if err != nil {
		return nil, fmt.Errorf("调用taskmanager服务失败: %w", err)
	}

	var response struct {
		Success bool `json:"success"`
		Data    struct {
			URLs []URLQueueItem `json:"urls"`
		} `json:"data"`
	}

	if err := json.Unmarshal(resp, &response); err != nil {
		return nil, fmt.Errorf("解析响应数据失败: %w", err)
	}

	if !response.Success {
		return nil, fmt.Errorf("获取待处理URL失败")
	}

	log.Printf("成功获取到 %d 个待处理URL", len(response.Data.URLs))
	return response.Data.URLs, nil
}

// processURL 处理单个URL
func (d *DownloaderService) processURL(ctx context.Context, urlItem URLQueueItem) {
	log.Printf("开始处理URL: ID=%d, URL=%s", urlItem.ID, urlItem.URL)

	// 更新状态为processing
	if err := d.updateURLStatus(ctx, urlItem.ID, "processing", ""); err != nil {
		log.Printf("更新URL状态失败: %v", err)
		return
	}

	// 执行下载
	err := d.downloadURL(ctx, urlItem)
	if err != nil {
		log.Printf("下载URL失败: %v", err)
		// 更新状态为failed
		d.updateURLStatus(ctx, urlItem.ID, "failed", err.Error())
		return
	}

	// 更新状态为completed
	if err := d.updateURLStatus(ctx, urlItem.ID, "completed", ""); err != nil {
		log.Printf("更新URL状态失败: %v", err)
		return
	}

	log.Printf("URL处理完成: ID=%d", urlItem.ID)
}

// downloadURL 下载URL
func (d *DownloaderService) downloadURL(ctx context.Context, urlItem URLQueueItem) error {
	var req *http.Request
	var err error

	if urlItem.Method == "POST" {
		req, err = http.NewRequestWithContext(ctx, "POST", urlItem.URL, bytes.NewBufferString(urlItem.Body))
		if err != nil {
			return err
		}
		req.Header.Set("Content-Type", "application/json")
	} else {
		req, err = http.NewRequestWithContext(ctx, "GET", urlItem.URL, nil)
		if err != nil {
			return err
		}
	}

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	// 读取响应内容
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}

	log.Printf("下载完成: URL=%s, Status=%d, Size=%d bytes", urlItem.URL, resp.StatusCode, len(body))

	// 这里可以添加解析逻辑，将解析结果保存到数据库或发送到其他服务
	// 例如：调用解析器服务处理下载的内容

	return nil
}

// updateURLStatus 更新URL状态
func (d *DownloaderService) updateURLStatus(ctx context.Context, urlID int64, status, errorMessage string) error {
	// 构建请求路径
	methodPath := fmt.Sprintf("api/v1/urls/%d/status", urlID)

	// 构建请求体
	payload := map[string]string{
		"status": status,
	}
	if errorMessage != "" {
		payload["error_message"] = errorMessage
	}

	data, err := json.Marshal(payload)
	if err != nil {
		return fmt.Errorf("序列化请求数据失败: %w", err)
	}

	// 使用 Dapr 服务调用
	content := &client.DataContent{
		ContentType: "application/json",
		Data:        data,
	}

	_, err = d.daprClient.InvokeMethodWithContent(ctx, d.taskManagerAppID, methodPath, "PUT", content)
	if err != nil {
		return fmt.Errorf("调用taskmanager服务更新URL状态失败: %w", err)
	}

	log.Printf("通过Dapr成功更新URL状态: ID=%d, Status=%s", urlID, status)
	return nil
}

// Close 关闭服务
func (d *DownloaderService) Close() error {
	if d.daprClient != nil {
		d.daprClient.Close()
	}
	return nil
}

func main() {
	// 创建下载器服务，使用 Dapr 应用ID
	downloader, err := NewDownloaderService("crawler-task-manager")
	if err != nil {
		log.Fatalf("创建下载器服务失败: %v", err)
	}
	defer downloader.Close()

	// 启动服务
	ctx := context.Background()
	if err := downloader.Start(ctx); err != nil {
		log.Fatalf("启动下载器服务失败: %v", err)
	}

	// 等待中断信号
	select {}
}
