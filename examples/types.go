package main

import "time"

// URLQueueItem URL队列项结构（简化版）
type URLQueueItem struct {
	ID       int64  `json:"id"`
	TaskID   string `json:"task_id"`
	URL      string `json:"url"`
	Method   string `json:"method"`
	Body     string `json:"body,omitempty"`
	Priority int    `json:"priority"`
}

// TaskCreatedEvent 任务创建事件结构
type TaskCreatedEvent struct {
	TaskID    string    `json:"task_id"`
	TaskName  string    `json:"task_name"`
	Priority  int       `json:"priority"`
	URLCount  int       `json:"url_count"`
	CreatedAt time.Time `json:"created_at"`
}
