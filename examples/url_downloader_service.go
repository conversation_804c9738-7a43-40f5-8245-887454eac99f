package main

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"time"

	"github.com/dapr/go-sdk/client"
	"github.com/dapr/go-sdk/service/common"
	daprd "github.com/dapr/go-sdk/service/http"
)

// URLDownloaderService URL下载服务
type URLDownloaderService struct {
	daprClient       client.Client
	taskManagerAppID string
	httpClient       *http.Client
	pubsubName       string
	topicName        string
}

// NewURLDownloaderService 创建URL下载服务
func NewURLDownloaderService(taskManagerAppID, pubsubName, topicName string) (*URLDownloaderService, error) {
	daprClient, err := client.NewClient()
	if err != nil {
		return nil, fmt.Errorf("创建Dapr客户端失败: %w", err)
	}

	return &URLDownloaderService{
		daprClient:       daprClient,
		taskManagerAppID: taskManagerAppID,
		pubsubName:       pubsubName,
		topicName:        topicName,
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
	}, nil
}

// Start 启动URL下载服务
func (d *URLDownloaderService) Start(ctx context.Context) error {
	log.Println("启动URL下载服务...")

	// 创建Dapr HTTP服务
	s := daprd.NewService(":8081")

	// 订阅Pubsub事件
	subscription := &common.Subscription{
		PubsubName: d.pubsubName,
		Topic:      d.topicName,
		Route:      "/download-url",
	}

	if err := s.AddTopicEventHandler(subscription, d.handleURLDownload); err != nil {
		return fmt.Errorf("添加事件处理器失败: %w", err)
	}

	log.Println("URL下载服务已启动，等待Pubsub消息...")

	// 启动服务
	return s.Start()
}

// handleURLDownload 处理URL下载事件
func (d *URLDownloaderService) handleURLDownload(ctx context.Context, e *common.TopicEvent) (retry bool, err error) {
	log.Printf("收到URL下载事件: %s", string(e.RawData))

	var urlItem URLQueueItem
	if err := json.Unmarshal(e.RawData, &urlItem); err != nil {
		log.Printf("解析URL数据失败: %v", err)
		return false, err
	}

	log.Printf("开始处理URL: ID=%d, URL=%s", urlItem.ID, urlItem.URL)

	// 更新状态为processing
	if err := d.updateURLStatus(ctx, urlItem.ID, "processing", ""); err != nil {
		log.Printf("更新URL状态失败: %v", err)
		return true, err // 重试
	}

	// 执行下载
	err = d.downloadURL(ctx, urlItem)
	if err != nil {
		log.Printf("下载URL失败: %v", err)
		// 更新状态为failed
		d.updateURLStatus(ctx, urlItem.ID, "failed", err.Error())
		return false, err // 不重试，标记为失败
	}

	// 更新状态为completed
	if err := d.updateURLStatus(ctx, urlItem.ID, "completed", ""); err != nil {
		log.Printf("更新URL状态失败: %v", err)
		return true, err // 重试
	}

	log.Printf("URL处理完成: ID=%d", urlItem.ID)
	return false, nil
}

// downloadURL 下载URL
func (d *URLDownloaderService) downloadURL(ctx context.Context, urlItem URLQueueItem) error {
	var req *http.Request
	var err error

	if urlItem.Method == "POST" {
		req, err = http.NewRequestWithContext(ctx, "POST", urlItem.URL, bytes.NewBufferString(urlItem.Body))
		if err != nil {
			return err
		}
		req.Header.Set("Content-Type", "application/json")
	} else {
		req, err = http.NewRequestWithContext(ctx, "GET", urlItem.URL, nil)
		if err != nil {
			return err
		}
	}

	resp, err := d.httpClient.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	// 读取响应内容
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}

	log.Printf("下载完成: URL=%s, Status=%d, Size=%d bytes", urlItem.URL, resp.StatusCode, len(body))

	// 这里可以添加解析逻辑，将解析结果保存到数据库或发送到其他服务
	// 例如：调用解析器服务处理下载的内容

	return nil
}

// updateURLStatus 更新URL状态
func (d *URLDownloaderService) updateURLStatus(ctx context.Context, urlID int64, status, errorMessage string) error {
	// 构建请求路径
	methodPath := fmt.Sprintf("api/v1/urls/%d/status", urlID)

	// 构建请求体
	payload := map[string]string{
		"status": status,
	}
	if errorMessage != "" {
		payload["error_message"] = errorMessage
	}

	data, err := json.Marshal(payload)
	if err != nil {
		return fmt.Errorf("序列化请求数据失败: %w", err)
	}

	// 使用 Dapr 服务调用
	content := &client.DataContent{
		ContentType: "application/json",
		Data:        data,
	}

	_, err = d.daprClient.InvokeMethodWithContent(ctx, d.taskManagerAppID, methodPath, "PUT", content)
	if err != nil {
		return fmt.Errorf("调用taskmanager服务更新URL状态失败: %w", err)
	}

	log.Printf("通过Dapr成功更新URL状态: ID=%d, Status=%s", urlID, status)
	return nil
}

// Close 关闭服务
func (d *URLDownloaderService) Close() error {
	if d.daprClient != nil {
		d.daprClient.Close()
	}
	return nil
}

func main() {
	// 创建URL下载服务，使用 Dapr 应用ID
	downloader, err := NewURLDownloaderService("crawler-task-manager", "pubsub", "url-download")
	if err != nil {
		log.Fatalf("创建URL下载服务失败: %v", err)
	}
	defer downloader.Close()

	// 启动服务
	ctx := context.Background()
	if err := downloader.Start(ctx); err != nil {
		log.Fatalf("启动URL下载服务失败: %v", err)
	}
}
