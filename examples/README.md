# 下载器服务示例

这个示例展示了如何使用 Dapr 框架的 sidecar 模式来访问 taskmanager 服务。

## 功能特性

### 使用 Dapr 服务调用的优势

1. **自动服务发现**: 无需硬编码服务地址，通过 Dapr 应用ID 自动发现服务
2. **负载均衡**: Dapr 自动处理多实例间的负载均衡
3. **重试机制**: 内置的重试和容错机制
4. **链路追踪**: 自动的分布式链路追踪
5. **安全通信**: 支持 mTLS 等安全通信机制
6. **服务网格集成**: 可以与 Istio 等服务网格无缝集成

### 主要修改

1. **服务结构体更新**:
   - 将 `taskManagerURL` 改为 `taskManagerAppID`
   - 移除对具体 URL 的依赖

2. **getPendingURLs 方法**:
   - 使用 `daprClient.InvokeMethod()` 替代直接 HTTP 调用
   - 自动处理服务发现和负载均衡

3. **updateURLStatus 方法**:
   - 使用 `daprClient.InvokeMethodWithContent()` 发送 PUT 请求
   - 支持 JSON 内容类型

## 使用方法

### 前置条件

1. 确保 Redis 已启动（用于状态存储和发布订阅）:
   ```bash
   make redis-start
   ```

2. 确保 MySQL 已启动（用于 URL 队列）:
   ```bash
   make mysql-start
   ```

### 启动服务

1. **启动 taskmanager 和 downloader 服务**:
   ```bash
   # 使用专门的配置文件同时启动两个服务
   dapr run -f examples/downloader-dapr-config.yaml
   ```

2. **或者分别启动服务**:
   ```bash
   # 终端1: 启动 taskmanager 服务
   dapr run -f dapr/run-dev.yaml
   
   # 终端2: 启动 downloader 服务
   cd examples
   dapr run --app-id crawler-downloader \
            --dapr-http-port 3501 \
            --dapr-grpc-port 50002 \
            --components-path ../dapr/components \
            --config ../dapr/config.yaml \
            -- go run downloader_service.go
   ```

### 测试功能

1. **创建一个爬虫任务**:
   ```bash
   curl -X POST http://localhost:8080/api/v1/tasks \
     -H "Content-Type: application/json" \
     -d '{
       "name": "测试任务",
       "description": "Dapr服务调用测试",
       "initial_urls": [
         {"url": "https://httpbin.org/get", "method": "GET"},
         {"url": "https://httpbin.org/post", "method": "POST", "body": "{\"test\": \"data\"}"}
       ],
       "priority": 2,
       "downloader_name": "dapr_downloader",
       "parser_name": "default_parser"
     }'
   ```

2. **观察下载器服务日志**:
   - 下载器服务会每5秒轮询一次待处理的URL
   - 通过 Dapr 调用 taskmanager 服务获取URL
   - 处理URL并通过 Dapr 更新状态

3. **检查URL处理状态**:
   ```bash
   # 获取待处理的URL
   curl "http://localhost:8080/api/v1/urls/pending?limit=10"
   
   # 查询所有URL状态
   curl "http://localhost:8080/api/v1/urls"
   ```

## 代码说明

### 服务调用示例

```go
// 获取待处理URL - GET请求
methodPath := fmt.Sprintf("api/v1/urls/pending?limit=%d", limit)
resp, err := d.daprClient.InvokeMethod(ctx, d.taskManagerAppID, methodPath, "GET")

// 更新URL状态 - PUT请求
content := &client.DataContent{
    ContentType: "application/json",
    Data:        data,
}
_, err = d.daprClient.InvokeMethodWithContent(ctx, d.taskManagerAppID, methodPath, "PUT", content)
```

### 配置说明

- **taskManagerAppID**: 目标服务的 Dapr 应用ID (`crawler-task-manager`)
- **Dapr端口**: 
  - taskmanager: HTTP 3500, gRPC 50001
  - downloader: HTTP 3501, gRPC 50002

## 注意事项

1. **事件订阅功能**: 当前版本暂未实现 Dapr 事件订阅，使用定期轮询模式
2. **错误处理**: 包含完善的错误处理和日志记录
3. **资源清理**: 服务关闭时会正确关闭 Dapr 客户端

## 后续改进

1. 实现 Dapr 事件订阅功能，替代定期轮询
2. 添加更多的配置选项（轮询间隔、并发数等）
3. 实现更复杂的重试和错误处理策略
4. 添加监控和指标收集功能
